需求概要说明书
以下是根据文档整理的赛事小程序需求说明书（合同附件版），按模块结构化呈现关键功能点： 
1. 项目概述
1.1 项目名称
光河赛事小程序
1.2 项目目标
构建一个完整的赛事活动管理平台，支持线上线下赛事的全流程管理，包括报名、参赛、评分、榜单、互动等功能。
2. 系统架构
2.1 总体架构
- 后端管理系统：活动运营、用户管理、数据统计
- 前端小程序：用户参赛、互动、榜单展示
- 数据中心：数据统计分析、报表生成
3. 后端管理系统需求
3.1 活动运营管理
3.1.1 互动控台
- 打榜刷票管理
  - 票数统计与监控
  - 刷票行为检测
  - 投票规则设置
- 弃赛补位机制
  - 自动补位规则配置
  - 手动补位操作
  - 补位通知系统
- 评委打分系统
  - 评分标准设置
  - 评委权限管理
  - 评分结果统计
3.1.2 线下大屏手机端
- 活动营销功能
  - 实时数据展示
  - 互动环节控制
  - 现场氛围营造
3.1.3 成长配置
- 虚拟道具管理
  - 道具类型定义
  - 道具效果设置
  - 道具获取规则
- 投票榜单设置
  - 榜单类型配置
  - 排名算法设置
  - 榜单更新频率
3.2 组队分组管理
3.2.1 创建组队
- 组队规则设置
  - 队伍人数限制
  - 组队条件设定
  - 队长权限配置
- 组队操作
  - 创建战队
  - 邀请成员
  - 队员管理
3.3 任务管理系统
3.3.1 任务创建与管理
- 任务设置
  - 任务类型定义
  - 任务奖励配置
  - 任务完成条件
- 签到系统
  - 签到场景选择
  - 签到积分规则
  - 二维码签到
  - 比赛场次关联
3.3.2 积分系统
- 积分规则
  - 签到积分
  - 任务积分
  - 奖励积分
- 积分使用
  - 兑换道具
  - 购买特权
  - 榜单排名
3.4 赛事推广计划
3.4.1 计划管理
- 推广计划列表
  - 计划名称设置
  - 计划目标配置
  - 运营目标设定
- 场次管理
  - 添加场次计划
  - 场次时间安排
  - 场次容量设置
3.4.2 数据统计
- 关键指标
  - 弃赛总人数/弃赛率
  - 激活总人数/激活率
  - 晋级总人数/晋级率
  - 任务总数/完成率
4. 季赛配置需求
4.1 基础设置
4.1.1 赛事类型
- 赛事周期
  - 季度赛
  - 全年赛
- 适用地区设置
  - 地区选择
  - 地区权限配置
4.1.2 参赛配置
- 参赛对象
  - 参赛条件设定
  - 参赛资格审核
- 参赛奖项
  - 奖项设置（图标、名称）
  - 奖项等级划分
  - 奖励机制
4.2 时间周期管理
4.2.1 时间设置
- 时间配置
  - 开始和结束时间设置
  - 时间选择器
  - 手动结束功能
- 时间控制
  - 从今天起开始
  - 自定义开始时间
4.3 阶段设置
4.3.1 赛程阶段
- 阶段配置
  - 第一阶段设置
  - 阶段名称输入（如海选期/声量突围战等）
  - 场次安排
- 晋级轮次
  - 添加轮次功能
  - 海选期线上初选
  - 海选期线下初赛
4.4 晋级设置
4.4.1 晋级方式
- 晋级规则
  - 单场前N名
  - 按比例晋级
  - 晋级分数线
- 特殊通道
  - 人工控制
  - 特殊照顾机制
4.4.2 成长值构成
- 积分组成
  - 评委分
  - 任务分
  - 签到分
  - 人气分
- 人气分计算
  - 投票权重
  - 点赞权重
  - 播放量权重
4.5 复活机制
4.5.1 复活设置
- 复活开关
  - 开启/关闭复活机制
  - 复活轮次设定
- 复活方式
  - 原地复活
  - 升阶复活
- 积分处理
  - 保留积分
  - 扣除积分
4.5.2 复活途径
- 社交裂变复活
  - 分享获得复活机会
  - 邀请好友助力
- 付费复活
  - 购买"道具卡"
  - 重生卡功能
- 声域保险
  - 失败返还50%钻石
  - 保险购买机制
5. 活动管理需求
5.1 活动创建与管理
5.1.1 活动基础设置
- 活动组管理
  - 活动分类
  - 活动权限设置
- 活动列表
  - 成交金额统计
  - 浏览量/点击次数/点击率
  - 参与人数统计
- 活动状态控制
  - 已开启/暂停/结束
  - 状态切换操作
  - 编辑/删除/暂停/结束操作
5.1.2 报名设置
- 报名控制
  - 是否需要报名参与开关
  - 添加报名填写信息
  - 报名费设置（有/无）
5.2 活动展示配置
5.2.1 视觉展示
- 轮播图
  - 等比例约束（只约束宽度不约束高度）
- 活动标题
  - 图标上传
  - 微软雅黑粗体
  - 两行不超过40个字，超出显示"..."
- 标签组
  - 自动退标签
  - 往届奖项标签
  - 链接弹窗功能
5.2.2 内容展示
- 活动简介
  - 图标+文字展示
- 活动营销区
  - 活动时间展示
  - 奖项设置展示
  - 参赛人群介绍
  - 可展开/收起功能
  - 富文本编辑支持
- 参赛流程
  - 文字编辑功能
  - 可展开/收起
- 赛事现场
  - 图片展示
  - 图片更换功能
5.2.3 互动功能
- 客服链接
  - 客服入口设置
- 分享功能
  - 分享海报保存
  - 发送朋友功能
  - 多张海报可选
5.3 收费设置
5.3.1 收费管理
- 收费概览
  - 成交总金额统计
  - 进行中活动统计
  - 触达总人数
  - 参与总人数
  - 活动数量统计
5.3.2 财务管理
- 钻石系统
  - 提现功能
  - 充值功能
- 财务数据
  - 收支统计
  - 财务报表
5.4 内容管理
- 通知模版
  - 模版创建
  - 模版管理
  - 批量通知
6. 用户管理需求
6.1 参与用户管理
6.1.1 用户基础信息
- 用户档案
  - 用户ID
  - 成长积分
  - 成长值
  - 自定义标签
  - 联系方式
  - 地区信息
6.1.2 用户状态管理
- 参赛状态
  - 用户状态（进程）
  - 可复活状态
  - 淘汰状态
  - 海选（第一轮）/复赛状态
- 操作功能
  - 淘汰操作
  - 复活操作
  - 禁止晋级
  - 状态修改
6.2 角色管理
6.2.1 选手管理
- 数据统计
  - 报名人数
  - 参赛人数
  - 晋级数
  - 弃赛统计
- 选手操作
  - 选手信息编辑
  - 参赛资格管理
6.2.2 评委/导师管理
- 权限管理
  - 进入权限：邀请链接
  - 评分权限
- 功能权限
  - 赠送积分
  - 组队调控
  - 发布任务
  - 评分选手
6.2.3 服务中心
- 权限设置
  - 进入权限：参赛激活选手
- 服务功能
  - 参赛指导
  - 问题解答
6.3 用户列表与数据看板
- 赛区/地区管理
  - 收益统计
  - 战力统计
- 数据展示
  - 报名数据
  - 参赛数据
  - 晋级数据
  - 弃赛数据
7. 前端小程序需求
7.1 个人中心（我的）
7.1.1 用户信息
- 基础信息
  - 用户ID获取
  - 用户名（昵称）获取
  - 头像管理（未获取前用光河赛事logo）
  - 注册手机号
7.1.2 收益管理
- 收益功能
  - 收益账单查看
  - 实名认证
  - 提现功能
7.2 赛事控制台
7.2.1 成绩与组队
- 成绩查询
  - 个人成绩
  - 比赛结果
- 组队功能
  - 组队成员管理
  - 队长功能
  - 我的战队展示
  - 战队名称/logo设置
  - 组队记录查看
  - 申请调换功能
7.2.2 收益管理
- 我的收益
  - 收益详情
  - 收益来源
7.3 榜单系统
7.3.1 常规榜单
- 榜单周期
  - 周榜单
  - 月榜单
  - 季榜单（一季为一个赛事周期）
- 榜单类型
  - 人气榜：按刷票排行
  - 专业榜：按评委分排行
  - 综合榜（晋级榜）：累计各项成长积分
7.3.2 单场榜单
- 即时榜单
  - 当场榜单开启时关闭常规榜单
  - 比赛进行时的单场即时榜单
  - 活动结束榜单关闭
- 榜单内容
  - 即时人气榜
  - 即时专业榜
  - 即时晋级榜
7.3.3 奖励机制
- 奖金池设置
  - 规则算法
  - 单榜奖金
  - 个人拉票回馈奖励
- 人工干预
  - 后端可即时调控
7.4 签到打卡
7.4.1 签到功能
- 签到管理
  - 参赛进程跟踪
  - 签到积分获取
7.4.2 任务跟踪
- 赛程阶段管理
  - 提交资料
  - 上传视频
  - 激活参赛
7.5 首页功能
7.5.1 核心展示
- 战队PK区
  - 预告数据
  - 即时数据
- 直播功能
  - 直播链接
  - 暂定播放组件
  - 歌曲时间倒计时
  - 播放/停止/关闭控制
7.5.2 活动展示
- 活动列表
  - 活动卡片展示
- 数据中心
  - 数据概览
  - 参赛激活认证
  - 标签管理
  - 头像展示
  - 选手参赛信息
  - 选手名称
8. 参赛流程需求
8.1 选手参赛信息
8.1.1 信息填写流程
- 第一步：基础信息
  - 填写基础信息
  - 信息可维护
- 第二步：视频上传
  - 视频上传到抖音（抖音版）
  - 话题设置（后端控制开启关闭）
  - 上传参赛视频
8.1.2 上传选项
- 暂不上传
  - 作品未准备好的情况
  - 二次上传入口
  - 活动详情页按钮链接
  - 二次进入小程序
  - 主动弹窗
8.2 激活参赛资格
8.2.1 激活流程
- 激活成功
  - 参赛激活成功提示页
  - 选手参赛信息入数据库
  - 覆盖预保存信息
8.2.2 时间管理
- 启程时间
  - 需要开启抖音上传视频功能才有效
- 保护期
  - 72小时数据维护保护期
  - 进入初选轨道
- 有效期
  - 资格有效期设置
  - 激活后在有效期内未参加海选，取消资格
  - 列入黑名单：本赛区不允许报名
8.3 预约参赛场次
8.3.1 场次管理
- 后端场次补位规则
- 场次列表
  - 按参赛阶段分类
8.4 参赛海报生成
8.4.1 海报功能
- 模板生成
  - 参赛海报模板
- 专属二维码
  - 必须可扫码功能
8.4.2 裂变引导文案
- 针对选手分享
  - "这是我的参赛宣言！"
  - "邀请你见证我的创意之旅！"
- 针对扫码者行动
  - "扫码为我加油助威！"
  - "扫码直达赛事，一起挑战！"
  - "扫码报名，和我同台竞技！"
8.4.3 二维码链接功能
- 选手个人展示页
  - 展示选手信息
  - 作品展示（如果允许）
  - 拉票功能（如果有点赞环节）
  - 支持按钮
- 赛事主页/活动页
  - 让扫码者了解赛事详情
- 专属邀请/报名页（裂变关键）
  - 扫码后进入带有该选手"邀请码/邀请ID"的报名页面
  - 追踪新用户来源（裂变效果追踪）
8.5 基础信息管理
8.5.1 信息维护
- 专属ID
  - 昵称设置
  - 头像管理（允许选手换照片作为头像）
  - 添加赛事管家（企业微信）
  - 专属参赛ID
8.5.2 协议与草稿
- 协议管理
  - 阅读协议并同意
  - 上传视频后自动弹出参赛协议
- 草稿功能
  - 下一步操作
  - 保存草稿功能
8.5.3 手机号管理
- 手机号设置
  - 更换手机号功能
  - 提示弹窗：当月两次机会
  - 参赛联系手机号
  - 捆绑生成的参赛ID，保证唯一性
- 昵称获取
  - 自动获取昵称
8.5.4 报名按钮状态
- 付费/免费报名
- 未激活选手状态
  - 已填写资料提交未激活选手
  - 报名按钮文字变为"立即激活参赛"
  - 点击跳转上传视频激活页
- 已激活状态
  - 已成功激活的不能再填写报名信息
  - 报名按钮置灰
  - 按钮文字变为"已成功激活"
9. 活动详情页需求
9.1 页面结构
9.1.1 基础信息展示
- 标题设置
  - 主标题
  - 副标题
- 标签功能
  - 创建标签
  - 标签要带链接功能
  - 形式：弹窗或跳转单独页面
9.1.2 位置导航
- 线下参赛点
  - 位置导航功能
  - 开设的线下赛事地点列表
9.1.3 重点事项展示
- 分块介绍卡片
  - 报名时间
  - 赛事奖项
  - 参赛对象等重点事项
- 参赛流程说明
  - 流程图展示
- 图片详情
  - 随时可更换
9.1.4 功能入口
- 分享功能
- 客服链接
- 登录入口
9.2 浏览权限
- 登录前浏览权限
  - 可浏览活动详情页
- 个人中心页（我的）
  - 登录后功能
9.3 活动服务中心
- 服务功能设置
10. 数据统计需求
10.1 数据看板
10.1.1 基础数据统计
- 赛区/地区数据
  - 收益统计
  - 战力统计
- 选手数据
  - 报名人数
  - 参赛人数
  - 晋级数
  - 弃赛统计
10.1.2 数据概览
- 参赛数据
  - 参赛激活认证
  - 数据维护
- 标签管理
  - 标签统计
  - 标签分类
10.2 数据中心功能
- 数据展示
  - 实时数据更新
  - 历史数据查询
- 数据分析
  - 趋势分析
  - 对比分析
11. 技术需求
11.1 系统性能要求
- 响应时间：页面加载时间不超过3秒
- 并发处理：支持同时在线用户数不少于10000人
- 数据同步：实时数据更新延迟不超过1秒
11.2 兼容性要求
- 小程序平台：支持微信小程序、抖音小程序
- 设备兼容：支持iOS、Android系统
- 分辨率适配：支持主流手机分辨率
11.3 安全性要求
- 数据安全：用户信息加密存储
- 权限控制：分级权限管理
- 防刷机制：防止恶意刷票、刷分